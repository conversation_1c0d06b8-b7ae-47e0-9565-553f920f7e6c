# 订单查询流程优化说明

## 优化概述

已成功优化订单查询流程，现在系统能够自动点击详情按钮，进入订单详情页面提取完整的订单信息和物流信息。

## 新的查询模板

优化后的查询结果将按以下格式显示：

```
亲，为您查询到手机尾号为【0292】的订单信息如下（最多显示3条）：

[订单查询信息]：
  - 订单号: 3727921284018481152
  - 下单时间: 2025/05/06 1115
  - 订单状态: 已完成
  - 快递公司：申通快递
  - 快递单号：772044720598340
  - 快递最新信息：【驿站】包裹已签收！如有问题请联系：兔喜快递超市13218180571 (2025-07-16 17:13:35)
```

## 优化功能

### 1. 自动点击详情按钮
- 系统会自动识别并点击订单列表中的"详情"按钮
- 支持多种详情按钮选择器：`.optLink`、`[data-v-150a3422=""]`等
- 如果找不到详情按钮，会降级到基本信息显示

### 2. 智能信息提取
- **订单信息提取**：从订单详情页面提取完整的订单状态、下单时间等
- **物流信息提取**：从物流信息区域提取快递公司、快递单号、最新物流状态
- **多重选择器**：使用多种CSS选择器确保信息提取的准确性

### 3. 页面导航管理
- 自动等待详情页面加载完成
- 智能返回订单列表页面
- 支持返回按钮点击和浏览器后退两种方式

## 技术实现

### 核心函数

1. **`extractDetailedOrderInfo`**
   - 主要的订单详细信息提取函数
   - 处理多个订单（最多3个）
   - 包含错误处理和降级机制

2. **`extractOrderDetailInfo`**
   - 从订单详情页面提取具体信息
   - 支持物流信息和订单信息的多重提取策略

3. **`waitForOrderDetailPage`**
   - 等待订单详情页面加载完成
   - 最多等待5秒，防止无限等待

4. **`returnToOrderList`**
   - 智能返回订单列表页面
   - 验证返回是否成功

### 信息提取策略

#### 物流信息提取
- **快递公司**：从 `.under_line .text` 的第一个元素提取
- **快递单号**：从 `.under_line .text` 的第二个元素提取，自动去除"物流编号："前缀
- **最新物流**：从 `.circle_green_tips` 区域提取状态和时间

#### 订单信息提取
- **订单号**：从订单列表页面的基本信息提取
- **下单时间**：从订单列表页面的基本信息提取
- **订单状态**：从订单列表页面的基本信息提取

## 错误处理机制

### 1. 降级处理
- 如果详情页面加载失败，系统会降级到显示基本订单信息
- 如果某个订单处理失败，会继续处理下一个订单

### 2. 超时保护
- 页面加载等待最多10次尝试（5秒）
- 返回列表页面等待最多5次尝试（2.5秒）

### 3. 日志记录
- 详细的状态监控日志记录每个步骤
- 区分信息、警告、错误等不同级别的日志

## 兼容性

### 支持的页面元素
- 详情按钮：`.optLink`、`[data-v-150a3422=""]`等
- 物流信息：`[data-v-73d19f36=""]`、`.logistics-info`等
- 订单信息：`[data-v-362d71cc=""]`、`.order-info`等

### 浏览器兼容性
- 支持现代浏览器的异步操作
- 使用标准的DOM API和事件处理

## 使用说明

1. 用户发送包含手机号后四位的订单查询请求
2. 系统自动在新标签页打开订单列表页面
3. 自动输入手机号并点击查询
4. 对每个找到的订单自动点击详情按钮
5. 提取完整的订单和物流信息
6. 返回订单列表继续处理下一个订单
7. 将所有信息整合后发送给用户

## 注意事项

- 系统最多处理3个订单，避免过长的响应时间
- 如果页面结构发生变化，可能需要更新选择器
- 建议在低峰期使用，避免对服务器造成过大压力
