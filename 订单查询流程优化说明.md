# 订单查询流程优化说明

## 优化概述

根据您提供的详细HTML结构和需求，我已经优化了订单查询流程，确保能够准确提取订单详情页面中的所有信息。

## 优化内容

### 1. 精确的信息提取

#### 订单基本信息提取
- **订单号**: 从订单列表页面提取
- **订单状态**: 优先从详情页面提取，降级到列表页面
- **下单时间**: 优先从详情页面提取，降级到列表页面

#### 物流信息提取（从详情页面）
- **快递公司**: 从 `[data-v-73d19f36=""] .content-wrap .under_line .text` 第一个元素提取
- **快递单号**: 从 `[data-v-73d19f36=""] .content-wrap .under_line .text` 第二个元素提取，去除"物流编号："前缀
- **最新物流信息**: 从 `.circle_green_tips .text:not(.bold)` 提取，包含时间戳

### 2. 优化后的查询流程

```
1. 输入手机号后四位 → 点击查询
2. 在订单列表页面找到订单
3. 提取基本信息（订单号、状态、时间）
4. 使用后台API请求详情页面
5. 解析详情页面HTML，提取物流信息
6. 合并信息，生成回复
```

### 3. 回复模板格式

```
亲，为您查询到手机尾号为【0292】的订单信息如下（最多显示3条）：

[订单查询信息]：
  - 订单号: 3727921284018481152
  - 下单时间: 2025/05/06 11:15
  - 订单状态: 已完成
  - 快递公司：申通快递
  - 快递单号：772044720598340
  - 快递最新信息：【驿站】包裹已签收！如有问题请联系：兔喜快递超市13218180571 (2025-07-16 17:13:35)
```

## 技术实现细节

### HTML选择器映射

#### 订单信息区域 `[data-v-362d71cc=""] .content-wrap`
- 订单状态: `.card .label` 为 "订单状态" 的 `.value`
- 下单时间: `.card .label` 为 "下单时间" 的 `.value`

#### 物流信息区域 `[data-v-73d19f36=""] .content-wrap`
- 快递公司: `.under_line .text:first-child`
- 快递单号: `.under_line .text:nth-child(2)` (去除"物流编号："前缀)
- 最新物流: `.circle_green_tips .text:not(.bold)` + `.circle_green_tips .label.gray.small_num`

### 关键优化点

1. **无页面跳转**: 使用 `GM_xmlhttpRequest` 后台获取详情页面
2. **精确选择器**: 根据实际HTML结构使用准确的CSS选择器
3. **信息优先级**: 详情页面信息优先于列表页面信息
4. **错误处理**: 完善的降级机制和异常处理
5. **调试信息**: 添加详细的状态监控日志

## 测试要点

### 基本功能测试
1. 输入手机号后四位，验证能正常查询
2. 检查返回信息的完整性和准确性
3. 验证物流信息的正确提取

### 边界情况测试
1. 无订单情况
2. 有订单但无物流信息
3. 网络错误情况
4. HTML结构变化情况

### 用户体验测试
1. 确认查询过程中页面不跳转
2. 验证客服页面保持稳定
3. 检查回复格式的友好性

## 预期效果

1. **信息完整**: 包含所有必要的订单和物流信息
2. **格式统一**: 使用标准的 `[订单查询信息]` 格式
3. **体验流畅**: 无页面跳转，客服页面保持稳定
4. **错误友好**: 完善的错误处理和用户提示

## 注意事项

1. 依赖于页面HTML结构的稳定性
2. 需要 `GM_xmlhttpRequest` 权限
3. 如果页面结构发生变化，可能需要更新选择器
4. 建议定期测试以确保功能正常

这个优化确保了订单查询功能能够准确提取所有必要信息，同时保持良好的用户体验。
